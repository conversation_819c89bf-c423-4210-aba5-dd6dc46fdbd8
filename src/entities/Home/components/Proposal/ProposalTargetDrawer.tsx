import { useState } from 'react';
import {
  Container,
  Drawer,
  Skeleton,
  Typography,
} from '@ghq-abi/design-system-v2';

import { ComparativeStep } from '~/entities/Proposal/components/Comparative/ComparativeStep';
import { FinalStep } from '~/entities/Proposal/components/Final/FinalStep';
import { TabEmptyState } from '~/entities/Proposal/components/TabEmptyState';
import { Avatar, ChildCard, Tab, TargetCard } from '~/shared/components';
import { Proposal } from '~/shared/types/Proposal';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { ProposalTargetOnly } from './ProposalTargetOnly';

interface ProposalTargetDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  proposal: Proposal | undefined;
  hasManagerPermission?: boolean;
  hasEmployeePermission?: boolean;
  isLoading?: boolean;
}

export const ProposalTargetDrawer = ({
  isOpen,
  onClose,
  proposal,
  hasManagerPermission,
  hasEmployeePermission,
  isLoading,
}: ProposalTargetDrawerProps) => {
  const [step, setStep] = useState(1);

  if (!proposal) {
    return null;
  }

  const targets = proposal?.targets ?? [];
  const targetsLength = targets.length;

  const changeStep = (stepNumber: number) => {
    setStep(stepNumber);
  };

  const changeClose = () => {
    onClose();
    setStep(1);
  };

  const feedbackTargets =
    targetsLength > 0
      ? targets?.filter(target =>
          target.targetTypes?.some(
            targetType => targetType.type === TargetTypeEnum.FEEDBACK,
          ),
        )
      : [];

  const stepComponents: Record<number, React.ReactNode> = {
    1: (
      <ProposalTargetOnly
        targets={proposal?.targets || []}
        proposalUid={proposal?.uid ?? ''}
        proposalStatus={proposal?.status}
        hasManagerPermission={hasManagerPermission}
        hasEmployeePermission={hasEmployeePermission}
        isDrawer
      />
    ),

    2: (
      <Container className="flex flex-col gap-4 h-full">
        {feedbackTargets.length > 0 ? (
          feedbackTargets.map(target =>
            target.children && target.children.length > 1 ? (
              <TargetCard
                key={target.uid}
                data={target}
                proposalStatus={proposal?.status}
                hideChildren={target.children.length <= 1}
                currentTargetType={TargetTypeEnum.FEEDBACK}
                hasManagerPermission={hasManagerPermission}
                hasEmployeePermission={hasEmployeePermission}
                isDrawer
              />
            ) : (
              <ChildCard
                key={target.uid}
                target={target}
                disableDrag
                hasManagerPermission={hasManagerPermission}
                hasEmployeePermission={hasEmployeePermission}
                isDrawer
              />
            ),
          )
        ) : (
          <TabEmptyState
            title="No feedback targets available"
            description="There are no feedback targets to display at this time."
          />
        )}
      </Container>
    ),

    3: (
      <FinalStep
        targets={proposal?.targets || []}
        proposalStatus={proposal?.status}
      />
    ),

    4:
      proposal?.status === ProposalStatusEnum.COMPLETED ? (
        <ComparativeStep
          targets={proposal?.targets || []}
          proposalStatus={proposal?.status}
        />
      ) : null,
  };

  return (
    <Drawer.Root direction="right" open={isOpen} onOpenChange={changeClose}>
      <Drawer.Content className="w-full max-w-lg pt-12">
        <Drawer.Title hidden />
        <Drawer.Description hidden />
        <Container className="flex flex-col gap-4 p-4">
          {isLoading && (
            <div className="flex items-center gap-4">
              <Skeleton className="w-[40px] h-[40px] rounded-full flex-shrink-0" />
              <Skeleton className="w-full h-[20px]" />
            </div>
          )}
          {!isLoading && proposal && (
            <Container className="flex items-center gap-2">
              <Avatar
                name={proposal?.employee?.name}
                globalId={`${proposal?.employee?.globalId}`}
              />
              <Typography variant="body-sm-bold">
                {proposal?.employee?.name}
              </Typography>
            </Container>
          )}

          <Tab
            step={step}
            changeStep={changeStep}
            tabs={[
              { title: 'Proposal', stepNumber: 1 },
              { title: 'Feedback', stepNumber: 2 },
              { title: 'Final', stepNumber: 3 },
              ...(proposal?.status === ProposalStatusEnum.COMPLETED
                ? [{ title: 'Comparative', stepNumber: 4 }]
                : []),
            ]}
            isLoading={isLoading}
          >
            {isLoading && !proposal
              ? Array.from({ length: 4 }).map((_, index) => (
                  <Skeleton key={index} className="w-full h-[400px]" />
                ))
              : stepComponents[step]}
          </Tab>
        </Container>
      </Drawer.Content>
    </Drawer.Root>
  );
};
