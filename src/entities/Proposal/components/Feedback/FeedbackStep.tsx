import React from 'react';
import { Container } from '@ghq-abi/design-system-v2';

import { ChildCard, TargetCard } from '~/shared/components/TargetCard';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum, UserPermissionsEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { TabEmptyState } from '../TabEmptyState';

import { FeedbackDrag } from './FeedbackDrag';

interface FeedbackStepProps {
  targets: Target[];
  proposalUid: string;
  proposalStatus?: ProposalStatusEnum;
  hasManagerPermission?: boolean;
  hasEmployeePermission?: boolean;
  isDrawer?: boolean;
  onOpenTargetComments?: (targetId: string) => void;
}

export function FeedbackStep({
  targets,
  proposalUid,
  proposalStatus,
  hasManagerPermission,
  hasEmployeePermission,
}: FeedbackStepProps) {
  const feedbackTargets = targets.filter(target =>
    target.targetTypes?.some(
      targetType => targetType.type === TargetTypeEnum.FEEDBACK,
    ),
  );

  if (
    feedbackTargets.length === 0 &&
    hasEmployeePermission &&
    proposalStatus !== ProposalStatusEnum.IN_PROGRESS_FEEDBACK
  ) {
    return (
      <TabEmptyState
        title="No feedback targets available"
        description="There are no feedback targets to display at this time."
      />
    );
  }

  return (
    <>
      {proposalStatus === ProposalStatusEnum.IN_PROGRESS_FEEDBACK &&
      hasEmployeePermission ? (
        <FeedbackDrag
          proposalStatus={proposalStatus}
          proposalUid={proposalUid}
          targets={feedbackTargets}
          allTargets={targets}
        />
      ) : (proposalStatus !== ProposalStatusEnum.NOT_STARTED &&
          proposalStatus !== ProposalStatusEnum.IN_PROGRESS_FEEDBACK) ||
        hasManagerPermission ? (
        <Container className="flex flex-col gap-4">
          {feedbackTargets.map(target => {
            if (target.children && target.children.length > 1) {
              return (
                <TargetCard
                  key={target.uid}
                  data={target}
                  proposalStatus={proposalStatus}
                  hideChildren={target.children.length <= 1}
                  currentTargetType={TargetTypeEnum.FEEDBACK}
                  onOpenComments={(target) => onOpenTargetComments?.(target.uid || '')}
                  hasManagerPermission={hasManagerPermission}
                  hasEmployeePermission={hasEmployeePermission}
                  isDrawer
                />
              );
            }
            return (
              <ChildCard key={target.uid} target={target} disableDrag={true} />
            );
          })}
        </Container>
      ) : (
        <FeedbackDrag
          proposalStatus={proposalStatus}
          proposalUid={proposalUid}
          targets={feedbackTargets}
        />
      )}
    </>
  );
}
